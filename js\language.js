// Language Management System
class LanguageManager {
    constructor() {
        this.currentLanguage = this.getStoredLanguage() || 'ar';
        this.translations = translations || {};
        this.init();
    }
    
    init() {
        this.setLanguage(this.currentLanguage);
        this.bindEvents();
    }
    
    // Get stored language from localStorage
    getStoredLanguage() {
        return localStorage.getItem('restaurant-language');
    }
    
    // Store language in localStorage
    storeLanguage(language) {
        localStorage.setItem('restaurant-language', language);
    }
    
    // Set current language
    setLanguage(language) {
        if (!this.translations[language]) {
            console.warn(`Language ${language} not found, falling back to Arabic`);
            language = 'ar';
        }
        
        this.currentLanguage = language;
        this.storeLanguage(language);
        this.updateHTML(language);
        this.updateDirection(language);
        this.updateLanguageToggle(language);
        
        // Trigger custom event for language change
        document.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: language }
        }));
    }
    
    // Update HTML lang attribute and direction
    updateHTML(language) {
        document.documentElement.lang = language;
    }
    
    // Update text direction (RTL for Arabic, LTR for English)
    updateDirection(language) {
        const direction = language === 'ar' ? 'rtl' : 'ltr';
        document.documentElement.dir = direction;
        document.body.className = document.body.className.replace(/\b(rtl|ltr)\b/g, '');
        document.body.classList.add(direction);
    }
    
    // Update language toggle button
    updateLanguageToggle(language) {
        const toggleButton = document.getElementById('languageToggle');
        const currentLangSpan = document.getElementById('currentLang');
        
        if (toggleButton && currentLangSpan) {
            const nextLanguage = language === 'ar' ? 'EN' : 'ع';
            currentLangSpan.textContent = nextLanguage;
            toggleButton.setAttribute('aria-label', 
                language === 'ar' ? 'Switch to English' : 'التبديل إلى العربية'
            );
        }
    }
    
    // Get translation for a key
    translate(key, language = null) {
        const lang = language || this.currentLanguage;
        return this.translations[lang]?.[key] || key;
    }
    
    // Update all translatable elements
    updateTranslations() {
        const elements = document.querySelectorAll('[data-translate]');
        
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.translate(key);
            
            // Update different types of elements
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'submit' || element.type === 'button') {
                    element.value = translation;
                } else {
                    element.placeholder = translation;
                }
            } else if (element.tagName === 'IMG') {
                element.alt = translation;
            } else if (element.hasAttribute('title')) {
                element.title = translation;
            } else {
                element.textContent = translation;
            }
        });
        
        // Update meta tags
        this.updateMetaTags();
    }
    
    // Update meta tags for SEO
    updateMetaTags() {
        const titleElement = document.querySelector('title[data-translate]');
        const descriptionElement = document.querySelector('meta[name="description"][data-translate]');
        
        if (titleElement) {
            const key = titleElement.getAttribute('data-translate');
            document.title = this.translate(key);
        }
        
        if (descriptionElement) {
            const key = descriptionElement.getAttribute('data-translate');
            descriptionElement.content = this.translate(key);
        }
    }
    
    // Toggle between languages
    toggleLanguage() {
        const newLanguage = this.currentLanguage === 'ar' ? 'en' : 'ar';
        this.setLanguage(newLanguage);
    }
    
    // Bind event listeners
    bindEvents() {
        // Language toggle button
        const languageToggle = document.getElementById('languageToggle');
        if (languageToggle) {
            languageToggle.addEventListener('click', () => {
                this.toggleLanguage();
            });
        }
        
        // Listen for DOM changes to translate new elements
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.translateNewElements(node);
                        }
                    });
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        // Initial translation
        setTimeout(() => {
            this.updateTranslations();
        }, 100);
    }
    
    // Translate newly added elements
    translateNewElements(container) {
        const elements = container.querySelectorAll('[data-translate]');
        elements.forEach(element => {
            const key = element.getAttribute('data-translate');
            const translation = this.translate(key);
            
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'submit' || element.type === 'button') {
                    element.value = translation;
                } else {
                    element.placeholder = translation;
                }
            } else if (element.tagName === 'IMG') {
                element.alt = translation;
            } else {
                element.textContent = translation;
            }
        });
    }
    
    // Get current language
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    // Check if current language is RTL
    isRTL() {
        return this.currentLanguage === 'ar';
    }
    
    // Format numbers based on language
    formatNumber(number) {
        if (this.currentLanguage === 'ar') {
            // Convert to Arabic-Indic numerals
            const arabicNumerals = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
            return number.toString().replace(/\d/g, (digit) => arabicNumerals[digit]);
        }
        return number.toString();
    }
    
    // Format currency based on language
    formatCurrency(amount) {
        const currency = this.translate('currency');
        const formattedAmount = this.formatNumber(amount);
        
        if (this.currentLanguage === 'ar') {
            return `${formattedAmount} ${currency}`;
        } else {
            return `${currency} ${formattedAmount}`;
        }
    }
    
    // Format date based on language
    formatDate(date) {
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        
        const locale = this.currentLanguage === 'ar' ? 'ar-SA' : 'en-US';
        return new Intl.DateTimeFormat(locale, options).format(date);
    }
    
    // Get localized content from objects
    getLocalizedContent(content) {
        if (typeof content === 'object' && content !== null) {
            return content[this.currentLanguage] || content['ar'] || content['en'] || '';
        }
        return content || '';
    }
}

// Initialize language manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.languageManager = new LanguageManager();
});

// Utility functions for global use
function translate(key, language = null) {
    return window.languageManager ? window.languageManager.translate(key, language) : key;
}

function getCurrentLanguage() {
    return window.languageManager ? window.languageManager.getCurrentLanguage() : 'ar';
}

function isRTL() {
    return window.languageManager ? window.languageManager.isRTL() : true;
}

function formatCurrency(amount) {
    return window.languageManager ? window.languageManager.formatCurrency(amount) : `${amount} ريال`;
}

function getLocalizedContent(content) {
    return window.languageManager ? window.languageManager.getLocalizedContent(content) : content;
}
