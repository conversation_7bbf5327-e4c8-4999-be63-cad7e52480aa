/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #d4af37;
    --secondary-color: #8b4513;
    --accent-color: #ff6b35;
    --dark-color: #2c1810;
    --light-color: #f8f5f0;
    --white: #ffffff;
    --gray-light: #f5f5f5;
    --gray-medium: #888;
    --gray-dark: #333;
    
    /* Typography */
    --font-arabic: 'Cairo', sans-serif;
    --font-english: 'Playfair Display', serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    --font-size-4xl: 2.5rem;
    
    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 8px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 16px rgba(0,0,0,0.2);
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    
    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* RTL/LTR Support */
html[lang="ar"] {
    font-family: var(--font-arabic);
    direction: rtl;
}

html[lang="en"] {
    font-family: var(--font-english);
    direction: ltr;
}

body {
    line-height: 1.6;
    color: var(--gray-dark);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: var(--spacing-sm);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }

p {
    margin-bottom: var(--spacing-sm);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    text-decoration: none;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: var(--font-size-base);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar {
    padding: var(--spacing-sm) 0;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-sm);
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.logo-img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.logo-text {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-lg);
}

.nav-link {
    text-decoration: none;
    color: var(--gray-dark);
    font-weight: 500;
    transition: color var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.language-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.language-toggle:hover {
    background: var(--secondary-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-xs);
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--gray-dark);
    margin: 3px 0;
    transition: all var(--transition-fast);
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.hero-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 24, 16, 0.7), rgba(212, 175, 55, 0.3));
    z-index: -1;
}

.hero-content {
    text-align: center;
    color: var(--white);
    max-width: 800px;
    padding: 0 var(--spacing-sm);
}

.hero-title {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    flex-wrap: wrap;
}

.scroll-indicator {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    color: var(--white);
    font-size: var(--font-size-xl);
    animation: bounce 2s infinite;
}

/* Sections */
section {
    padding: var(--spacing-2xl) 0;
}

.section-title {
    text-align: center;
    margin-bottom: var(--spacing-xl);
    color: var(--dark-color);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-sm);
}

/* Features Section */
.features {
    background: var(--gray-light);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.feature-card h3 {
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
}

.feature-card p {
    color: var(--gray-medium);
    line-height: 1.6;
}

/* Popular Dishes Section */
.dishes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.dish-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.dish-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.dish-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.dish-content {
    padding: var(--spacing-md);
}

.dish-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-xs);
    color: var(--dark-color);
}

.dish-description {
    color: var(--gray-medium);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.dish-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

/* Testimonials Section */
.testimonials {
    background: var(--dark-color);
    color: var(--white);
}

.testimonials .section-title {
    color: var(--white);
}

.testimonials-slider {
    max-width: 800px;
    margin: 0 auto;
}

.testimonial-card {
    text-align: center;
    padding: var(--spacing-xl);
}

.testimonial-content p {
    font-size: var(--font-size-lg);
    font-style: italic;
    margin-bottom: var(--spacing-lg);
    line-height: 1.8;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.testimonial-author img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
}

.rating {
    color: var(--primary-color);
    margin-top: var(--spacing-xs);
}

/* Footer */
.footer {
    background: var(--dark-color);
    color: var(--white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.footer-logo img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
}

.footer-logo h3 {
    color: var(--primary-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-link:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

.footer-section h4 {
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-xs);
}

.footer-links a {
    color: var(--white);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.contact-item i {
    color: var(--primary-color);
    width: 20px;
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255,255,255,0.1);
    color: var(--gray-medium);
}

/* Loading Screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    pointer-events: none;
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--gray-light);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-md);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* Page Header Styles */
.page-header {
    position: relative;
    height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: 80px;
}

.page-header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.page-header-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.page-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(44, 24, 16, 0.8), rgba(212, 175, 55, 0.4));
    z-index: -1;
}

.page-header-content {
    text-align: center;
    color: var(--white);
    max-width: 600px;
}

.page-title {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.page-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
}

/* Menu Page Styles */
.menu-controls {
    background: var(--gray-light);
    padding: var(--spacing-lg) 0;
}

.menu-controls-content {
    display: grid;
    grid-template-columns: 1fr auto auto;
    gap: var(--spacing-lg);
    align-items: center;
}

.search-container {
    position: relative;
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.search-icon {
    position: absolute;
    left: var(--spacing-sm);
    color: var(--gray-medium);
    z-index: 1;
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-sm) var(--spacing-sm) 40px;
    border: 2px solid var(--gray-light);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.category-filters {
    display: flex;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.category-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    background: var(--white);
    border: 2px solid var(--gray-light);
    border-radius: var(--radius-md);
    color: var(--gray-dark);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.category-btn:hover,
.category-btn.active {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.sort-options {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.sort-select {
    padding: var(--spacing-xs) var(--spacing-sm);
    border: 2px solid var(--gray-light);
    border-radius: var(--radius-md);
    background: var(--white);
    cursor: pointer;
}

.menu-section {
    padding: var(--spacing-2xl) 0;
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: var(--spacing-lg);
}

.dish-image-container {
    position: relative;
    overflow: hidden;
}

.popular-badge,
.vegetarian-badge,
.spicy-badge {
    position: absolute;
    top: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    color: var(--white);
}

.popular-badge {
    right: var(--spacing-sm);
    background: var(--accent-color);
}

.vegetarian-badge {
    left: var(--spacing-sm);
    background: #4caf50;
}

.spicy-badge {
    left: var(--spacing-sm);
    top: 50px;
    background: #f44336;
}

.dish-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: var(--spacing-sm);
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.cart-icon {
    position: relative;
    margin-right: var(--spacing-sm);
}

.cart-counter {
    position: absolute;
    top: -8px;
    right: -8px;
    background: var(--accent-color);
    color: var(--white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-xs);
    font-weight: 600;
}

/* About Page Styles */
.our-story {
    padding: var(--spacing-2xl) 0;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.story-image img {
    width: 100%;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

.mission-vision {
    background: var(--gray-light);
    padding: var(--spacing-2xl) 0;
}

.mission-vision-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
}

.mission-card,
.vision-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
}

.card-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.statistics {
    padding: var(--spacing-2xl) 0;
    background: var(--dark-color);
    color: var(--white);
}

.statistics .section-title {
    color: var(--white);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    text-align: center;
    padding: var(--spacing-lg);
}

.stat-icon {
    font-size: var(--font-size-3xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-sm);
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--white);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--gray-medium);
    font-size: var(--font-size-lg);
}

.our-team {
    padding: var(--spacing-2xl) 0;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.team-member {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.team-member:hover {
    transform: translateY(-5px);
}

.member-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.member-info {
    padding: var(--spacing-lg);
    text-align: center;
}

.member-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
}

.our-values {
    background: var(--gray-light);
    padding: var(--spacing-2xl) 0;
}

.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.value-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.value-card:hover {
    transform: translateY(-5px);
}

.value-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: var(--white);
    font-size: var(--font-size-xl);
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid,
    .dishes-grid {
        grid-template-columns: 1fr;
    }

    .testimonial-author {
        flex-direction: column;
        text-align: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .menu-controls-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .mission-vision-grid {
        grid-template-columns: 1fr;
    }

    .page-header {
        height: 40vh;
    }

    .page-title {
        font-size: var(--font-size-2xl);
    }
}

/* Contact Page Styles */
.contact-info-section {
    padding: var(--spacing-2xl) 0;
}

.contact-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.contact-card {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
    box-shadow: var(--shadow-md);
    transition: transform var(--transition-normal);
}

.contact-card:hover {
    transform: translateY(-5px);
}

.contact-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: var(--white);
    font-size: var(--font-size-2xl);
}

.contact-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: var(--secondary-color);
}

.hours-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-form-section {
    background: var(--gray-light);
    padding: var(--spacing-2xl) 0;
}

.contact-form-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.contact-features {
    margin-top: var(--spacing-lg);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-sm);
}

.feature-item i {
    color: var(--primary-color);
}

.contact-form {
    background: var(--white);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-weight: 600;
    color: var(--gray-dark);
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: var(--spacing-sm);
    border: 2px solid var(--gray-light);
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

.btn-full {
    width: 100%;
    justify-content: center;
}

.map-section {
    padding: var(--spacing-lg) 0;
}

.map-container {
    height: 400px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.map-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
}

.map-content {
    text-align: center;
}

.map-content i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-md);
}

.reservation-section {
    background: var(--gray-light);
    padding: var(--spacing-2xl) 0;
}

.reservation-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
}

.reservation-features {
    margin: var(--spacing-lg) 0;
}

.reservation-buttons {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.reservation-image img {
    width: 100%;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
}

/* Special Offers Styles */
.special-offers {
    background: var(--dark-color);
    color: var(--white);
    padding: var(--spacing-2xl) 0;
}

.special-offers .section-title {
    color: var(--white);
}

.offers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
}

.offer-card {
    background: var(--white);
    color: var(--gray-dark);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    position: relative;
}

.offer-badge {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: var(--accent-color);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    z-index: 1;
}

.offer-content {
    padding: var(--spacing-xl);
}

.offer-price {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-md);
}

.old-price {
    text-decoration: line-through;
    color: var(--gray-medium);
}

.new-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-color);
}

.offer-validity {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    margin-top: var(--spacing-md);
    color: var(--gray-medium);
    font-size: var(--font-size-sm);
}

/* Chef Recommendations */
.chef-recommendations {
    padding: var(--spacing-2xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);
}

.section-subtitle {
    color: var(--gray-medium);
    font-size: var(--font-size-lg);
    margin-top: var(--spacing-sm);
}

.recommendations-slider {
    max-width: 800px;
    margin: 0 auto;
}

.recommendation-card {
    background: var(--gray-light);
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    text-align: center;
}

.chef-quote {
    margin-bottom: var(--spacing-lg);
}

.chef-quote i {
    font-size: var(--font-size-2xl);
    color: var(--primary-color);
    margin-bottom: var(--spacing-md);
}

.chef-quote p {
    font-size: var(--font-size-lg);
    font-style: italic;
    line-height: 1.8;
}

.chef-info {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);
}

.chef-image {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.chef-details h4 {
    margin-bottom: var(--spacing-xs);
}

.chef-details p {
    color: var(--gray-medium);
    font-size: var(--font-size-sm);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 100px;
    right: var(--spacing-lg);
    background: var(--white);
    color: var(--gray-dark);
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    z-index: 10000;
    transform: translateX(400px);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification-success {
    border-left: 4px solid #4caf50;
}

.notification-error {
    border-left: 4px solid #f44336;
}

.notification-info {
    border-left: 4px solid var(--primary-color);
}

/* Dish Modal */
.dish-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-lg);
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-lg);
    max-width: 500px;
    width: 100%;
    position: relative;
    overflow: hidden;
}

.modal-close {
    position: absolute;
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    cursor: pointer;
    color: var(--white);
    z-index: 1;
}

.modal-content img {
    width: 100%;
    height: 250px;
    object-fit: cover;
}

.modal-content h2,
.modal-content p,
.modal-content .price,
.modal-content .btn {
    padding: 0 var(--spacing-lg);
}

.modal-content h2 {
    margin: var(--spacing-md) 0;
}

.modal-content .btn {
    margin: var(--spacing-lg);
    width: calc(100% - 2 * var(--spacing-lg));
}

/* Cart Modal Styles */
.cart-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    padding: var(--spacing-lg);
}

.cart-modal .modal-content {
    background: var(--white);
    border-radius: var(--radius-lg);
    max-width: 600px;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--gray-light);
}

.modal-header h2 {
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: var(--spacing-lg);
}

.cart-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-bottom: 1px solid var(--gray-light);
}

.cart-item:last-child {
    border-bottom: none;
}

.cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-md);
    object-fit: cover;
}

.cart-item-details {
    flex: 1;
}

.cart-item-details h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
}

.cart-item-price {
    color: var(--primary-color);
    font-weight: 600;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid var(--gray-light);
    background: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.quantity-btn:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.quantity {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
}

.remove-item {
    background: none;
    border: none;
    color: var(--accent-color);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: background var(--transition-fast);
}

.remove-item:hover {
    background: rgba(255, 107, 53, 0.1);
}

.cart-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-light);
}

.cart-total {
    text-align: center;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-lg);
}

.cart-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

/* Enhanced Modal Styles */
.modal-body {
    padding: var(--spacing-lg);
}

.dish-details {
    margin: var(--spacing-lg) 0;
}

.ingredients-list {
    list-style: none;
    padding: 0;
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.ingredients-list li {
    background: var(--gray-light);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-sm);
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-top: 1px solid var(--gray-light);
}

.price-large {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
}

/* Dish Card Enhancements */
.dish-ingredients {
    margin: var(--spacing-sm) 0;
    color: var(--gray-medium);
    font-size: var(--font-size-sm);
}

.dish-ingredients i {
    margin-right: var(--spacing-xs);
}

/* Loading States */
.loading-indicator {
    text-align: center;
    padding: var(--spacing-2xl);
    display: none;
}

.loading-indicator.show {
    display: block;
}

/* Enhanced Notification Styles */
.notification {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    min-width: 300px;
}

.notification i {
    font-size: var(--font-size-lg);
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .contact-form-content,
    .reservation-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .reservation-buttons {
        justify-content: center;
    }

    .chef-info {
        flex-direction: column;
        text-align: center;
    }

    .notification {
        right: var(--spacing-sm);
        left: var(--spacing-sm);
        transform: translateY(-100px);
        min-width: auto;
    }

    .notification.show {
        transform: translateY(0);
    }

    .cart-modal {
        padding: var(--spacing-sm);
    }

    .cart-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-sm);
    }

    .cart-item-controls {
        align-self: center;
    }

    .cart-actions {
        flex-direction: column;
    }

    .modal-footer {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .ingredients-list {
        justify-content: center;
    }
}
