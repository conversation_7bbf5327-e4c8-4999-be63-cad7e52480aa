// Translations for Arabic and English
const translations = {
    ar: {
        // Site Meta
        site_title: "مطعم الذواقة - أشهى الأطباق العربية والعالمية",
        site_description: "مطعم الذواقة يقدم أشهى الأطباق العربية والعالمية في أجواء راقية ومميزة",
        
        // Navigation
        restaurant_name: "مطعم الذواقة",
        nav_home: "الرئيسية",
        nav_menu: "المنيو",
        nav_about: "حولنا",
        nav_contact: "تواصل معنا",
        
        // Hero Section
        hero_title: "مرحباً بكم في مطعم الذواقة",
        hero_subtitle: "تجربة طعام استثنائية تجمع بين النكهات الأصيلة والأجواء الراقية",
        view_menu: "استعرض المنيو",
        book_table: "احجز طاولة",
        
        // Features Section
        why_choose_us: "لماذا تختارنا؟",
        fresh_ingredients: "مكونات طازجة",
        fresh_ingredients_desc: "نستخدم أجود المكونات الطازجة يومياً لضمان أفضل طعم ونكهة",
        expert_chefs: "طهاة خبراء",
        expert_chefs_desc: "فريق من أمهر الطهاة المتخصصين في المأكولات العربية والعالمية",
        premium_service: "خدمة مميزة",
        premium_service_desc: "خدمة عملاء استثنائية وأجواء راقية لتجربة لا تُنسى",
        
        // Popular Dishes
        popular_dishes: "أطباقنا المميزة",
        view_full_menu: "استعرض المنيو كاملاً",
        
        // Menu Categories
        appetizers: "المقبلات",
        main_courses: "الأطباق الرئيسية",
        desserts: "الحلويات",
        beverages: "المشروبات",
        
        // Dishes
        hummus: "حمص بالطحينة",
        hummus_desc: "حمص كريمي مع الطحينة وزيت الزيتون والصنوبر",
        tabbouleh: "تبولة",
        tabbouleh_desc: "سلطة البقدونس الطازجة مع الطماطم والبرغل وعصير الليمون",
        fattoush: "فتوش",
        fattoush_desc: "سلطة مشكلة مع الخبز المحمص والسماق",
        
        grilled_chicken: "دجاج مشوي",
        grilled_chicken_desc: "دجاج مشوي بالأعشاب والتوابل مع الأرز والخضار",
        lamb_kabsa: "كبسة لحم",
        lamb_kabsa_desc: "أرز بسمتي مع لحم الخروف والتوابل العربية الأصيلة",
        fish_sayadieh: "صيادية سمك",
        fish_sayadieh_desc: "سمك طازج مع الأرز والبصل المكرمل",
        
        baklava: "بقلاوة",
        baklava_desc: "حلوى شرقية بالفستق والعسل",
        muhallabia: "مهلبية",
        muhallabia_desc: "حلوى الحليب التقليدية مع ماء الورد والفستق",
        knafeh: "كنافة",
        knafeh_desc: "كنافة نابلسية بالجبن والقطر",
        
        arabic_coffee: "قهوة عربية",
        arabic_coffee_desc: "قهوة عربية أصيلة مع الهيل",
        mint_tea: "شاي بالنعناع",
        mint_tea_desc: "شاي أخضر طازج بالنعناع",
        fresh_juice: "عصير طازج",
        fresh_juice_desc: "عصائر طبيعية طازجة متنوعة",
        
        // Testimonials
        customer_reviews: "آراء عملائنا",
        testimonial_1: "تجربة رائعة! الطعام لذيذ جداً والخدمة ممتازة. أنصح الجميع بزيارة هذا المطعم.",
        
        // Footer
        footer_description: "مطعم الذواقة يقدم أشهى الأطباق العربية والعالمية في أجواء راقية ومميزة منذ عام 2010",
        quick_links: "روابط سريعة",
        contact_info: "معلومات التواصل",
        address: "شارع الملك فهد، الرياض، المملكة العربية السعودية",
        working_hours: "يومياً من 11:00 ص إلى 12:00 م",
        copyright: "© 2024 مطعم الذواقة. جميع الحقوق محفوظة.",
        
        // About Page
        about_title: "حول مطعم الذواقة",
        about_subtitle: "قصة نجاح تمتد لأكثر من عقد من الزمن",
        our_story: "قصتنا",
        our_story_desc: "تأسس مطعم الذواقة في عام 2010 برؤية واضحة: تقديم أشهى الأطباق العربية والعالمية في أجواء راقية ومميزة. منذ ذلك الحين، نسعى جاهدين لتقديم تجربة طعام استثنائية لعملائنا الكرام.",
        our_mission: "رسالتنا",
        our_mission_desc: "نسعى لأن نكون الوجهة الأولى لمحبي الطعام الأصيل والجودة العالية، من خلال تقديم أطباق مبتكرة تجمع بين التراث والحداثة.",
        our_vision: "رؤيتنا",
        our_vision_desc: "أن نصبح أفضل مطعم في المنطقة، معروف بجودة طعامه وخدمته المتميزة وأجوائه الراقية.",
        
        // Contact Page
        contact_title: "تواصل معنا",
        contact_subtitle: "نحن هنا لخدمتكم",
        get_in_touch: "ابق على تواصل",
        send_message: "أرسل رسالة",
        your_name: "اسمك",
        your_email: "بريدك الإلكتروني",
        your_phone: "رقم هاتفك",
        your_message: "رسالتك",
        send_button: "إرسال الرسالة",
        
        // Common
        loading: "جاري التحميل...",
        read_more: "اقرأ المزيد",
        learn_more: "تعلم المزيد",
        order_now: "اطلب الآن",
        add_to_cart: "أضف للسلة",
        price: "السعر",
        currency: "ريال",
        
        // Menu Page
        menu_title: "منيو مطعم الذواقة",
        menu_subtitle: "اكتشف مجموعتنا المتنوعة من الأطباق الشهية",
        filter_all: "الكل",
        sort_by: "ترتيب حسب",
        sort_name: "الاسم",
        sort_price: "السعر",
        
        // Error Messages
        error_loading: "خطأ في التحميل",
        try_again: "حاول مرة أخرى",
        page_not_found: "الصفحة غير موجودة",
        
        // Success Messages
        message_sent: "تم إرسال الرسالة بنجاح",
        thank_you: "شكراً لك",

        // Additional Menu Translations
        search_placeholder: "ابحث في المنيو...",
        sort_popular: "الأكثر طلباً",
        popular: "مشهور",
        added_to_cart: "تم إضافة الطبق إلى السلة",
        special_offers: "عروض خاصة",
        discount_20: "خصم 20%",
        family_meal: "وجبة عائلية",
        family_meal_desc: "وجبة مكونة من 4 أطباق رئيسية + مقبلات + مشروبات",
        buy_2_get_1: "اشتري 2 واحصل على 1 مجاناً",
        dessert_offer: "عرض الحلويات",
        dessert_offer_desc: "اشتري حلويتين واحصل على الثالثة مجاناً",
        valid_until: "صالح حتى نهاية الشهر",
        chef_recommendations: "توصيات الشيف",
        chef_recommendations_desc: "أطباق مختارة بعناية من قبل شيفنا التنفيذي",
        chef_quote_1: "هذا الطبق يجمع بين النكهات التقليدية والتقنيات الحديثة لتجربة طعام لا تُنسى",
        executive_chef: "الشيف التنفيذي",

        // About Page Additional
        story_continuation: "بدأت رحلتنا بحلم بسيط: إنشاء مكان يجمع بين التراث الأصيل والحداثة، حيث يمكن للعائلات والأصدقاء الاستمتاع بأوقات لا تُنسى حول مائدة مليئة بأشهى الأطباق.",
        our_achievements: "إنجازاتنا",
        happy_customers: "عميل سعيد",
        dishes_served: "طبق مميز",
        awards_won: "جائزة حصلنا عليها",
        years_experience: "سنة من الخبرة",
        our_team: "فريقنا",
        head_chef: "الشيف الرئيسي",
        chef_1_desc: "خبرة 15 سنة في المطابخ العربية والعالمية",
        pastry_chef: "شيف الحلويات",
        chef_2_desc: "متخصصة في الحلويات الشرقية والغربية",
        restaurant_manager: "مديرة المطعم",
        manager_desc: "تضمن تقديم أفضل خدمة للعملاء",
        our_values: "قيمنا",
        quality: "الجودة",
        quality_desc: "نلتزم بأعلى معايير الجودة في كل ما نقدمه",
        service: "الخدمة",
        service_desc: "نقدم خدمة استثنائية تفوق توقعات عملائنا",
        freshness: "الطزاجة",
        freshness_desc: "نستخدم أطزج المكونات يومياً",
        community: "المجتمع",
        community_desc: "نفخر بكوننا جزءاً من المجتمع المحلي",

        // Contact Page Additional
        our_location: "موقعنا",
        get_directions: "احصل على الاتجاهات",
        call_us: "اتصل بنا",
        call_now: "اتصل الآن",
        email_us: "راسلنا",
        send_email: "أرسل إيميل",
        opening_hours: "ساعات العمل",
        weekdays: "الأيام العادية:",
        weekends: "نهاية الأسبوع:",
        contact_form_desc: "نحن نحب أن نسمع منكم! سواء كان لديكم استفسار، اقتراح، أو تريدون حجز طاولة، لا تترددوا في التواصل معنا.",
        quick_response: "رد سريع خلال 24 ساعة",
        professional_service: "خدمة احترافية",
        multilingual_support: "دعم متعدد اللغات",
        subject: "الموضوع",
        select_subject: "اختر الموضوع",
        reservation: "حجز طاولة",
        general_inquiry: "استفسار عام",
        complaint: "شكوى",
        suggestion: "اقتراح",
        catering: "خدمة الضيافة",
        find_us: "اعثر علينا",
        restaurant_location: "موقع المطعم",
        open_in_maps: "افتح في الخرائط",
        make_reservation: "احجز طاولتك",
        reservation_desc: "احجز طاولتك مسبقاً لضمان الحصول على أفضل الأماكن في المطعم",
        easy_booking: "حجز سهل وسريع",
        flexible_timing: "مواعيد مرنة",
        group_bookings: "حجوزات جماعية",
        call_to_book: "اتصل للحجز",
        whatsapp_booking: "احجز عبر واتساب",

        // Additional Common
        no_results: "لا توجد نتائج",
        no_results_desc: "جرب البحث بكلمات مختلفة أو تصفح الفئات المختلفة",

        // Cart Translations
        cart: "السلة",
        cart_empty: "السلة فارغة",
        total: "المجموع",
        clear_cart: "إفراغ السلة",
        checkout: "إتمام الطلب",
        cart_cleared: "تم إفراغ السلة",
        ingredients: "المكونات",
        quantity: "الكمية"
    },
    
    en: {
        // Site Meta
        site_title: "Gourmet Restaurant - Finest Arabic & International Cuisine",
        site_description: "Gourmet Restaurant offers the finest Arabic and international dishes in an elegant and distinctive atmosphere",
        
        // Navigation
        restaurant_name: "Gourmet Restaurant",
        nav_home: "Home",
        nav_menu: "Menu",
        nav_about: "About",
        nav_contact: "Contact",
        
        // Hero Section
        hero_title: "Welcome to Gourmet Restaurant",
        hero_subtitle: "An exceptional dining experience that combines authentic flavors with elegant atmosphere",
        view_menu: "View Menu",
        book_table: "Book Table",
        
        // Features Section
        why_choose_us: "Why Choose Us?",
        fresh_ingredients: "Fresh Ingredients",
        fresh_ingredients_desc: "We use the finest fresh ingredients daily to ensure the best taste and flavor",
        expert_chefs: "Expert Chefs",
        expert_chefs_desc: "A team of skilled chefs specialized in Arabic and international cuisine",
        premium_service: "Premium Service",
        premium_service_desc: "Exceptional customer service and elegant atmosphere for an unforgettable experience",
        
        // Popular Dishes
        popular_dishes: "Our Signature Dishes",
        view_full_menu: "View Full Menu",
        
        // Menu Categories
        appetizers: "Appetizers",
        main_courses: "Main Courses",
        desserts: "Desserts",
        beverages: "Beverages",
        
        // Dishes
        hummus: "Hummus with Tahini",
        hummus_desc: "Creamy hummus with tahini, olive oil, and pine nuts",
        tabbouleh: "Tabbouleh",
        tabbouleh_desc: "Fresh parsley salad with tomatoes, bulgur, and lemon juice",
        fattoush: "Fattoush",
        fattoush_desc: "Mixed salad with toasted bread and sumac",
        
        grilled_chicken: "Grilled Chicken",
        grilled_chicken_desc: "Herb and spice grilled chicken with rice and vegetables",
        lamb_kabsa: "Lamb Kabsa",
        lamb_kabsa_desc: "Basmati rice with lamb and authentic Arabic spices",
        fish_sayadieh: "Fish Sayadieh",
        fish_sayadieh_desc: "Fresh fish with rice and caramelized onions",
        
        baklava: "Baklava",
        baklava_desc: "Middle Eastern pastry with pistachios and honey",
        muhallabia: "Muhallabia",
        muhallabia_desc: "Traditional milk pudding with rose water and pistachios",
        knafeh: "Knafeh",
        knafeh_desc: "Nabulsi knafeh with cheese and syrup",
        
        arabic_coffee: "Arabic Coffee",
        arabic_coffee_desc: "Authentic Arabic coffee with cardamom",
        mint_tea: "Mint Tea",
        mint_tea_desc: "Fresh green tea with mint",
        fresh_juice: "Fresh Juice",
        fresh_juice_desc: "Variety of fresh natural juices",
        
        // Testimonials
        customer_reviews: "Customer Reviews",
        testimonial_1: "Amazing experience! The food is delicious and the service is excellent. I recommend everyone to visit this restaurant.",
        
        // Footer
        footer_description: "Gourmet Restaurant has been serving the finest Arabic and international dishes in an elegant atmosphere since 2010",
        quick_links: "Quick Links",
        contact_info: "Contact Information",
        address: "King Fahd Street, Riyadh, Saudi Arabia",
        working_hours: "Daily from 11:00 AM to 12:00 AM",
        copyright: "© 2024 Gourmet Restaurant. All rights reserved.",
        
        // About Page
        about_title: "About Gourmet Restaurant",
        about_subtitle: "A success story spanning over a decade",
        our_story: "Our Story",
        our_story_desc: "Gourmet Restaurant was established in 2010 with a clear vision: to serve the finest Arabic and international dishes in an elegant and distinctive atmosphere. Since then, we strive to provide an exceptional dining experience for our valued customers.",
        our_mission: "Our Mission",
        our_mission_desc: "We strive to be the premier destination for lovers of authentic food and high quality, by offering innovative dishes that combine heritage and modernity.",
        our_vision: "Our Vision",
        our_vision_desc: "To become the best restaurant in the region, known for its food quality, outstanding service, and elegant atmosphere.",
        
        // Contact Page
        contact_title: "Contact Us",
        contact_subtitle: "We are here to serve you",
        get_in_touch: "Get in Touch",
        send_message: "Send Message",
        your_name: "Your Name",
        your_email: "Your Email",
        your_phone: "Your Phone",
        your_message: "Your Message",
        send_button: "Send Message",
        
        // Common
        loading: "Loading...",
        read_more: "Read More",
        learn_more: "Learn More",
        order_now: "Order Now",
        add_to_cart: "Add to Cart",
        price: "Price",
        currency: "SAR",
        
        // Menu Page
        menu_title: "Gourmet Restaurant Menu",
        menu_subtitle: "Discover our diverse collection of delicious dishes",
        filter_all: "All",
        sort_by: "Sort by",
        sort_name: "Name",
        sort_price: "Price",
        
        // Error Messages
        error_loading: "Loading Error",
        try_again: "Try Again",
        page_not_found: "Page Not Found",
        
        // Success Messages
        message_sent: "Message sent successfully",
        thank_you: "Thank You",

        // Additional Menu Translations
        search_placeholder: "Search menu...",
        sort_popular: "Most Popular",
        popular: "Popular",
        added_to_cart: "Item added to cart",
        special_offers: "Special Offers",
        discount_20: "20% Off",
        family_meal: "Family Meal",
        family_meal_desc: "Meal consisting of 4 main dishes + appetizers + beverages",
        buy_2_get_1: "Buy 2 Get 1 Free",
        dessert_offer: "Dessert Offer",
        dessert_offer_desc: "Buy two desserts and get the third one free",
        valid_until: "Valid until end of month",
        chef_recommendations: "Chef's Recommendations",
        chef_recommendations_desc: "Carefully selected dishes by our executive chef",
        chef_quote_1: "This dish combines traditional flavors with modern techniques for an unforgettable dining experience",
        executive_chef: "Executive Chef",

        // About Page Additional
        story_continuation: "Our journey began with a simple dream: to create a place that combines authentic heritage with modernity, where families and friends can enjoy unforgettable moments around a table filled with the most delicious dishes.",
        our_achievements: "Our Achievements",
        happy_customers: "Happy Customers",
        dishes_served: "Signature Dishes",
        awards_won: "Awards Won",
        years_experience: "Years of Experience",
        our_team: "Our Team",
        head_chef: "Head Chef",
        chef_1_desc: "15 years of experience in Arabic and international cuisine",
        pastry_chef: "Pastry Chef",
        chef_2_desc: "Specialized in Middle Eastern and Western desserts",
        restaurant_manager: "Restaurant Manager",
        manager_desc: "Ensures the best service for customers",
        our_values: "Our Values",
        quality: "Quality",
        quality_desc: "We commit to the highest quality standards in everything we offer",
        service: "Service",
        service_desc: "We provide exceptional service that exceeds our customers' expectations",
        freshness: "Freshness",
        freshness_desc: "We use the freshest ingredients daily",
        community: "Community",
        community_desc: "We are proud to be part of the local community",

        // Contact Page Additional
        our_location: "Our Location",
        get_directions: "Get Directions",
        call_us: "Call Us",
        call_now: "Call Now",
        email_us: "Email Us",
        send_email: "Send Email",
        opening_hours: "Opening Hours",
        weekdays: "Weekdays:",
        weekends: "Weekends:",
        contact_form_desc: "We love to hear from you! Whether you have an inquiry, suggestion, or want to book a table, don't hesitate to contact us.",
        quick_response: "Quick response within 24 hours",
        professional_service: "Professional service",
        multilingual_support: "Multilingual support",
        subject: "Subject",
        select_subject: "Select Subject",
        reservation: "Table Reservation",
        general_inquiry: "General Inquiry",
        complaint: "Complaint",
        suggestion: "Suggestion",
        catering: "Catering Service",
        find_us: "Find Us",
        restaurant_location: "Restaurant Location",
        open_in_maps: "Open in Maps",
        make_reservation: "Make a Reservation",
        reservation_desc: "Book your table in advance to ensure the best seats in the restaurant",
        easy_booking: "Easy and quick booking",
        flexible_timing: "Flexible timing",
        group_bookings: "Group bookings",
        call_to_book: "Call to Book",
        whatsapp_booking: "Book via WhatsApp",

        // Additional Common
        no_results: "No Results Found",
        no_results_desc: "Try searching with different keywords or browse different categories",

        // Cart Translations
        cart: "Cart",
        cart_empty: "Cart is empty",
        total: "Total",
        clear_cart: "Clear Cart",
        checkout: "Checkout",
        cart_cleared: "Cart cleared",
        ingredients: "Ingredients",
        quantity: "Quantity"
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = translations;
}
