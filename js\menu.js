// Advanced Menu Management System
class MenuManager {
    constructor() {
        this.currentCategory = 'all';
        this.currentSort = 'name';
        this.searchQuery = '';
        this.cart = this.loadCart();
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.updateCartDisplay();
        this.loadMenuContent();
    }
    
    // Load cart from localStorage
    loadCart() {
        return JSON.parse(localStorage.getItem('restaurant-cart') || '[]');
    }
    
    // Save cart to localStorage
    saveCart() {
        localStorage.setItem('restaurant-cart', JSON.stringify(this.cart));
        this.updateCartDisplay();
    }
    
    // Update cart display
    updateCartDisplay() {
        const cartCounter = document.getElementById('cartCounter');
        if (cartCounter) {
            const totalItems = this.cart.reduce((sum, item) => sum + item.quantity, 0);
            cartCounter.textContent = totalItems;
            cartCounter.style.display = totalItems > 0 ? 'block' : 'none';
        }
    }
    
    // Bind event listeners
    bindEvents() {
        // Search functionality
        const searchInput = document.getElementById('menuSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value.trim();
                this.filterAndDisplayDishes();
            });
        }
        
        // Sort functionality
        const sortSelect = document.getElementById('sortSelect');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.currentSort = e.target.value;
                this.filterAndDisplayDishes();
            });
        }
        
        // Cart icon click
        const cartIcon = document.querySelector('.cart-icon');
        if (cartIcon) {
            cartIcon.addEventListener('click', () => {
                this.showCartModal();
            });
        }
    }
    
    // Load and display menu content
    loadMenuContent() {
        this.loadCategories();
        this.filterAndDisplayDishes();
    }
    
    // Load category buttons
    loadCategories() {
        const categoriesContainer = document.getElementById('menuCategories');
        if (!categoriesContainer || !window.menuData) return;
        
        const categories = menuData.categories;
        
        categoriesContainer.innerHTML = `
            <button class="category-btn active" data-category="all">
                <i class="fas fa-th-large"></i>
                ${translate('filter_all')}
            </button>
            ${categories.map(category => `
                <button class="category-btn" data-category="${category.id}">
                    <i class="${category.icon}"></i>
                    ${getLocalizedContent(category.name)}
                </button>
            `).join('')}
        `;
        
        // Add click events to category buttons
        const categoryBtns = categoriesContainer.querySelectorAll('.category-btn');
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.currentCategory = btn.getAttribute('data-category');
                this.filterAndDisplayDishes();
            });
        });
    }
    
    // Filter and display dishes based on current filters
    filterAndDisplayDishes() {
        if (!window.menuData) return;
        
        let dishes = [...menuData.dishes];
        
        // Filter by category
        if (this.currentCategory !== 'all') {
            dishes = dishes.filter(dish => dish.category === this.currentCategory);
        }
        
        // Filter by search query
        if (this.searchQuery) {
            const currentLang = getCurrentLanguage();
            dishes = dishes.filter(dish => 
                getLocalizedContent(dish.name).toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                getLocalizedContent(dish.description).toLowerCase().includes(this.searchQuery.toLowerCase())
            );
        }
        
        // Sort dishes
        dishes = this.sortDishes(dishes);
        
        // Display dishes
        this.displayDishes(dishes);
        
        // Show/hide no results message
        this.toggleNoResults(dishes.length === 0);
    }
    
    // Sort dishes based on current sort option
    sortDishes(dishes) {
        const currentLang = getCurrentLanguage();
        
        switch (this.currentSort) {
            case 'name':
                return dishes.sort((a, b) => 
                    getLocalizedContent(a.name).localeCompare(getLocalizedContent(b.name))
                );
            case 'price':
                return dishes.sort((a, b) => a.price - b.price);
            case 'popular':
                return dishes.sort((a, b) => {
                    if (a.popular && !b.popular) return -1;
                    if (!a.popular && b.popular) return 1;
                    return 0;
                });
            default:
                return dishes;
        }
    }
    
    // Display dishes in the grid
    displayDishes(dishes) {
        const dishesContainer = document.getElementById('menuDishes');
        if (!dishesContainer) return;
        
        if (dishes.length === 0) {
            dishesContainer.innerHTML = '';
            return;
        }
        
        dishesContainer.innerHTML = dishes.map(dish => this.createDishCard(dish)).join('');
        
        // Add event listeners to dish cards
        this.bindDishEvents();
        
        // Trigger scroll animations
        this.triggerScrollAnimations();
    }
    
    // Create HTML for a dish card
    createDishCard(dish) {
        const badges = this.createDishBadges(dish);
        
        return `
            <div class="dish-card animate-on-scroll hover-lift" data-dish-id="${dish.id}" data-category="${dish.category}">
                <div class="dish-image-container">
                    <img src="${dish.image}" alt="${getLocalizedContent(dish.name)}" class="dish-image" loading="lazy" onerror="this.src='images/placeholder-dish.jpg'">
                    ${badges}
                </div>
                <div class="dish-content">
                    <h3 class="dish-title">${getLocalizedContent(dish.name)}</h3>
                    <p class="dish-description">${getLocalizedContent(dish.description)}</p>
                    <div class="dish-ingredients">
                        <small><i class="fas fa-list"></i> ${getLocalizedContent(dish.ingredients).slice(0, 3).join(', ')}</small>
                    </div>
                    <div class="dish-footer">
                        <div class="dish-price">${formatCurrency(dish.price)}</div>
                        <button class="btn btn-primary btn-sm add-to-cart" data-dish-id="${dish.id}">
                            <i class="fas fa-plus"></i>
                            ${translate('add_to_cart')}
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    // Create badges for dish card
    createDishBadges(dish) {
        let badges = '';
        
        if (dish.popular) {
            badges += `<span class="popular-badge">${translate('popular')}</span>`;
        }
        
        if (dish.vegetarian) {
            badges += `<span class="vegetarian-badge"><i class="fas fa-leaf"></i></span>`;
        }
        
        if (dish.spicy) {
            badges += `<span class="spicy-badge"><i class="fas fa-pepper-hot"></i></span>`;
        }
        
        return badges;
    }
    
    // Bind events to dish cards
    bindDishEvents() {
        const dishCards = document.querySelectorAll('.dish-card');
        const addToCartBtns = document.querySelectorAll('.add-to-cart');
        
        // Dish card click events
        dishCards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.closest('.add-to-cart')) {
                    const dishId = parseInt(card.getAttribute('data-dish-id'));
                    this.showDishModal(dishId);
                }
            });
        });
        
        // Add to cart button events
        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const dishId = parseInt(btn.getAttribute('data-dish-id'));
                this.addToCart(dishId);
            });
        });
    }
    
    // Add dish to cart
    addToCart(dishId) {
        const dish = menuData.getDishById(dishId);
        if (!dish) return;
        
        const existingItem = this.cart.find(item => item.id === dishId);
        
        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.cart.push({
                id: dishId,
                name: getLocalizedContent(dish.name),
                price: dish.price,
                image: dish.image,
                quantity: 1
            });
        }
        
        this.saveCart();
        this.showNotification(translate('added_to_cart'), 'success');
        
        // Add visual feedback
        const btn = document.querySelector(`[data-dish-id="${dishId}"].add-to-cart`);
        if (btn) {
            btn.classList.add('animate-pulse');
            setTimeout(() => btn.classList.remove('animate-pulse'), 600);
        }
    }
    
    // Remove item from cart
    removeFromCart(dishId) {
        this.cart = this.cart.filter(item => item.id !== dishId);
        this.saveCart();
    }
    
    // Update item quantity in cart
    updateCartItemQuantity(dishId, quantity) {
        const item = this.cart.find(item => item.id === dishId);
        if (item) {
            if (quantity <= 0) {
                this.removeFromCart(dishId);
            } else {
                item.quantity = quantity;
                this.saveCart();
            }
        }
    }
    
    // Show dish details modal
    showDishModal(dishId) {
        const dish = menuData.getDishById(dishId);
        if (!dish) return;
        
        const modal = document.createElement('div');
        modal.className = 'dish-modal';
        modal.innerHTML = `
            <div class="modal-content animate-scale-in">
                <button class="modal-close" aria-label="Close">&times;</button>
                <img src="${dish.image}" alt="${getLocalizedContent(dish.name)}" onerror="this.src='images/placeholder-dish.jpg'">
                <div class="modal-body">
                    <h2>${getLocalizedContent(dish.name)}</h2>
                    <p class="dish-description">${getLocalizedContent(dish.description)}</p>
                    <div class="dish-details">
                        <h4>${translate('ingredients')}:</h4>
                        <ul class="ingredients-list">
                            ${getLocalizedContent(dish.ingredients).map(ingredient => `<li>${ingredient}</li>`).join('')}
                        </ul>
                    </div>
                    <div class="modal-footer">
                        <div class="price-large">${formatCurrency(dish.price)}</div>
                        <button class="btn btn-primary" onclick="menuManager.addToCart(${dish.id}); document.body.removeChild(this.closest('.dish-modal'));">
                            <i class="fas fa-plus"></i>
                            ${translate('add_to_cart')}
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal events
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => document.body.removeChild(modal));
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) document.body.removeChild(modal);
        });
        
        // ESC key to close
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                document.body.removeChild(modal);
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
    }
    
    // Show cart modal
    showCartModal() {
        if (this.cart.length === 0) {
            this.showNotification(translate('cart_empty'), 'info');
            return;
        }
        
        const total = this.cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
        
        const modal = document.createElement('div');
        modal.className = 'cart-modal';
        modal.innerHTML = `
            <div class="modal-content animate-slide-in-up">
                <div class="modal-header">
                    <h2><i class="fas fa-shopping-cart"></i> ${translate('cart')}</h2>
                    <button class="modal-close" aria-label="Close">&times;</button>
                </div>
                <div class="cart-items">
                    ${this.cart.map(item => this.createCartItem(item)).join('')}
                </div>
                <div class="cart-footer">
                    <div class="cart-total">
                        <strong>${translate('total')}: ${formatCurrency(total)}</strong>
                    </div>
                    <div class="cart-actions">
                        <button class="btn btn-secondary" onclick="menuManager.clearCart(); document.body.removeChild(this.closest('.cart-modal'));">
                            ${translate('clear_cart')}
                        </button>
                        <button class="btn btn-primary">
                            <i class="fas fa-check"></i>
                            ${translate('checkout')}
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal events
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => document.body.removeChild(modal));
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) document.body.removeChild(modal);
        });
    }
    
    // Create cart item HTML
    createCartItem(item) {
        return `
            <div class="cart-item" data-item-id="${item.id}">
                <img src="${item.image}" alt="${item.name}" class="cart-item-image">
                <div class="cart-item-details">
                    <h4>${item.name}</h4>
                    <div class="cart-item-price">${formatCurrency(item.price)}</div>
                </div>
                <div class="cart-item-controls">
                    <button class="quantity-btn" onclick="menuManager.updateCartItemQuantity(${item.id}, ${item.quantity - 1})">-</button>
                    <span class="quantity">${item.quantity}</span>
                    <button class="quantity-btn" onclick="menuManager.updateCartItemQuantity(${item.id}, ${item.quantity + 1})">+</button>
                </div>
                <button class="remove-item" onclick="menuManager.removeFromCart(${item.id}); this.closest('.cart-item').remove();">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
    }
    
    // Clear cart
    clearCart() {
        this.cart = [];
        this.saveCart();
        this.showNotification(translate('cart_cleared'), 'info');
    }
    
    // Toggle no results message
    toggleNoResults(show) {
        const noResults = document.getElementById('noResults');
        if (noResults) {
            noResults.style.display = show ? 'block' : 'none';
        }
    }
    
    // Trigger scroll animations for new elements
    triggerScrollAnimations() {
        const animatedElements = document.querySelectorAll('.animate-on-scroll:not(.animated)');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}"></i>
            <span>${message}</span>
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => document.body.removeChild(notification), 300);
        }, 3000);
    }
}

// Initialize menu manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('menuDishes')) {
        window.menuManager = new MenuManager();
    }
});
