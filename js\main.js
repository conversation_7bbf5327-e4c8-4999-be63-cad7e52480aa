// Main JavaScript file for Restaurant Website
class RestaurantApp {
    constructor() {
        this.isLoading = true;
        this.currentPage = this.getCurrentPage();
        this.init();
    }
    
    init() {
        this.showLoadingScreen();
        this.bindEvents();
        this.initializeComponents();
        this.hideLoadingScreen();
    }
    
    // Get current page from URL
    getCurrentPage() {
        const path = window.location.pathname;
        const page = path.split('/').pop().replace('.html', '') || 'index';
        return page;
    }
    
    // Show loading screen
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loadingScreen');
        if (loadingScreen) {
            loadingScreen.classList.remove('hidden');
        }
    }
    
    // Hide loading screen
    hideLoadingScreen() {
        setTimeout(() => {
            const loadingScreen = document.getElementById('loadingScreen');
            if (loadingScreen) {
                loadingScreen.classList.add('hidden');
                setTimeout(() => {
                    loadingScreen.style.display = 'none';
                }, 500);
            }
            this.isLoading = false;
        }, 1500);
    }
    
    // Bind global event listeners
    bindEvents() {
        // Mobile menu toggle
        this.bindMobileMenuEvents();
        
        // Smooth scrolling for anchor links
        this.bindSmoothScrolling();
        
        // Scroll animations
        this.bindScrollAnimations();
        
        // Header scroll effect
        this.bindHeaderScrollEffect();
        
        // Form submissions
        this.bindFormEvents();
        
        // Social media links
        this.bindSocialMediaEvents();
        
        // Window resize events
        window.addEventListener('resize', this.debounce(() => {
            this.handleResize();
        }, 250));
        
        // Language change events
        document.addEventListener('languageChanged', (e) => {
            this.handleLanguageChange(e.detail.language);
        });
    }
    
    // Mobile menu events
    bindMobileMenuEvents() {
        const mobileMenuToggle = document.getElementById('mobileMenuToggle');
        const navMenu = document.querySelector('.nav-menu');
        
        if (mobileMenuToggle && navMenu) {
            mobileMenuToggle.addEventListener('click', () => {
                navMenu.classList.toggle('active');
                mobileMenuToggle.classList.toggle('active');
                document.body.classList.toggle('menu-open');
            });
            
            // Close menu when clicking on nav links
            const navLinks = navMenu.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    navMenu.classList.remove('active');
                    mobileMenuToggle.classList.remove('active');
                    document.body.classList.remove('menu-open');
                });
            });
        }
    }
    
    // Smooth scrolling for anchor links
    bindSmoothScrolling() {
        const anchorLinks = document.querySelectorAll('a[href^="#"]');
        anchorLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    const headerHeight = document.querySelector('.header').offsetHeight;
                    const targetPosition = targetElement.offsetTop - headerHeight;
                    
                    window.scrollTo({
                        top: targetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });
    }
    
    // Scroll animations
    bindScrollAnimations() {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animated');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        animatedElements.forEach(element => {
            observer.observe(element);
        });
    }
    
    // Header scroll effect
    bindHeaderScrollEffect() {
        const header = document.querySelector('.header');
        let lastScrollTop = 0;
        
        window.addEventListener('scroll', () => {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollTop > lastScrollTop && scrollTop > 200) {
                header.classList.add('hidden');
            } else {
                header.classList.remove('hidden');
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    // Form events
    bindFormEvents() {
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission(form);
            });
        });
    }
    
    // Social media events
    bindSocialMediaEvents() {
        const socialLinks = document.querySelectorAll('.social-link');
        socialLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const platform = this.getSocialPlatform(link);
                this.openSocialMedia(platform);
            });
        });
    }
    
    // Initialize page-specific components
    initializeComponents() {
        switch (this.currentPage) {
            case 'index':
                this.initHomePage();
                break;
            case 'menu':
                this.initMenuPage();
                break;
            case 'about':
                this.initAboutPage();
                break;
            case 'contact':
                this.initContactPage();
                break;
        }
    }
    
    // Initialize home page
    initHomePage() {
        this.loadPopularDishes();
        this.initTestimonialsSlider();
        this.initHeroAnimations();
    }
    
    // Load popular dishes
    loadPopularDishes() {
        const dishesContainer = document.getElementById('popularDishes');
        if (!dishesContainer || !window.menuData) return;
        
        const popularDishes = menuData.getPopularDishes().slice(0, 6);
        const currentLang = getCurrentLanguage();
        
        dishesContainer.innerHTML = popularDishes.map(dish => `
            <div class="dish-card animate-on-scroll hover-lift" data-dish-id="${dish.id}">
                <img src="${dish.image}" alt="${getLocalizedContent(dish.name)}" class="dish-image" loading="lazy">
                <div class="dish-content">
                    <h3 class="dish-title">${getLocalizedContent(dish.name)}</h3>
                    <p class="dish-description">${getLocalizedContent(dish.description)}</p>
                    <div class="dish-price">${formatCurrency(dish.price)}</div>
                </div>
            </div>
        `).join('');
        
        // Add click events to dish cards
        const dishCards = dishesContainer.querySelectorAll('.dish-card');
        dishCards.forEach(card => {
            card.addEventListener('click', () => {
                const dishId = card.getAttribute('data-dish-id');
                this.showDishDetails(dishId);
            });
        });
    }
    
    // Initialize testimonials slider
    initTestimonialsSlider() {
        const slider = document.getElementById('testimonialsSlider');
        if (!slider) return;
        
        // Simple testimonials rotation
        const testimonials = slider.querySelectorAll('.testimonial-card');
        let currentIndex = 0;
        
        if (testimonials.length > 1) {
            setInterval(() => {
                testimonials[currentIndex].classList.remove('active');
                currentIndex = (currentIndex + 1) % testimonials.length;
                testimonials[currentIndex].classList.add('active');
            }, 5000);
        }
    }
    
    // Initialize hero animations
    initHeroAnimations() {
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        const heroButtons = document.querySelector('.hero-buttons');
        
        setTimeout(() => {
            if (heroTitle) heroTitle.classList.add('animate-fade-in');
        }, 500);
        
        setTimeout(() => {
            if (heroSubtitle) heroSubtitle.classList.add('animate-fade-in');
        }, 800);
        
        setTimeout(() => {
            if (heroButtons) heroButtons.classList.add('animate-fade-in');
        }, 1100);
    }
    
    // Initialize menu page
    initMenuPage() {
        this.loadMenuCategories();
        this.loadAllDishes();
        this.initMenuFilters();
    }

    // Load menu categories
    loadMenuCategories() {
        const categoriesContainer = document.getElementById('menuCategories');
        if (!categoriesContainer || !window.menuData) return;

        const categories = menuData.categories;
        const currentLang = getCurrentLanguage();

        categoriesContainer.innerHTML = `
            <button class="category-btn active" data-category="all">${translate('filter_all')}</button>
            ${categories.map(category => `
                <button class="category-btn" data-category="${category.id}">
                    <i class="${category.icon}"></i>
                    ${getLocalizedContent(category.name)}
                </button>
            `).join('')}
        `;

        // Add click events
        const categoryBtns = categoriesContainer.querySelectorAll('.category-btn');
        categoryBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                categoryBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const category = btn.getAttribute('data-category');
                this.filterDishes(category);
            });
        });
    }

    // Load all dishes
    loadAllDishes() {
        const dishesContainer = document.getElementById('menuDishes');
        if (!dishesContainer || !window.menuData) return;

        this.renderDishes(menuData.dishes);
    }

    // Render dishes
    renderDishes(dishes) {
        const dishesContainer = document.getElementById('menuDishes');
        if (!dishesContainer) return;

        dishesContainer.innerHTML = dishes.map(dish => `
            <div class="dish-card animate-on-scroll hover-lift" data-dish-id="${dish.id}" data-category="${dish.category}">
                <div class="dish-image-container">
                    <img src="${dish.image}" alt="${getLocalizedContent(dish.name)}" class="dish-image" loading="lazy">
                    ${dish.popular ? '<span class="popular-badge">' + translate('popular') + '</span>' : ''}
                    ${dish.vegetarian ? '<span class="vegetarian-badge"><i class="fas fa-leaf"></i></span>' : ''}
                    ${dish.spicy ? '<span class="spicy-badge"><i class="fas fa-pepper-hot"></i></span>' : ''}
                </div>
                <div class="dish-content">
                    <h3 class="dish-title">${getLocalizedContent(dish.name)}</h3>
                    <p class="dish-description">${getLocalizedContent(dish.description)}</p>
                    <div class="dish-footer">
                        <div class="dish-price">${formatCurrency(dish.price)}</div>
                        <button class="btn btn-primary btn-sm add-to-cart" data-dish-id="${dish.id}">
                            <i class="fas fa-plus"></i>
                            ${translate('add_to_cart')}
                        </button>
                    </div>
                </div>
            </div>
        `).join('');

        // Add click events
        const dishCards = dishesContainer.querySelectorAll('.dish-card');
        dishCards.forEach(card => {
            card.addEventListener('click', (e) => {
                if (!e.target.classList.contains('add-to-cart')) {
                    const dishId = card.getAttribute('data-dish-id');
                    this.showDishDetails(dishId);
                }
            });
        });

        const addToCartBtns = dishesContainer.querySelectorAll('.add-to-cart');
        addToCartBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.stopPropagation();
                const dishId = btn.getAttribute('data-dish-id');
                this.addToCart(dishId);
            });
        });
    }

    // Filter dishes by category
    filterDishes(category) {
        if (category === 'all') {
            this.renderDishes(menuData.dishes);
        } else {
            const filteredDishes = menuData.getDishesByCategory(category);
            this.renderDishes(filteredDishes);
        }
    }

    // Initialize menu filters
    initMenuFilters() {
        const searchInput = document.getElementById('menuSearch');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                const query = e.target.value.trim();
                if (query.length > 0) {
                    const results = menuData.searchDishes(query, getCurrentLanguage());
                    this.renderDishes(results);
                } else {
                    this.renderDishes(menuData.dishes);
                }
            });
        }
    }

    // Add to cart functionality
    addToCart(dishId) {
        const dish = menuData.getDishById(parseInt(dishId));
        if (!dish) return;

        // Simple cart implementation
        let cart = JSON.parse(localStorage.getItem('restaurant-cart') || '[]');
        const existingItem = cart.find(item => item.id === dish.id);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            cart.push({
                id: dish.id,
                name: getLocalizedContent(dish.name),
                price: dish.price,
                image: dish.image,
                quantity: 1
            });
        }

        localStorage.setItem('restaurant-cart', JSON.stringify(cart));
        this.showNotification(translate('added_to_cart'), 'success');
        this.updateCartCounter();
    }

    // Update cart counter
    updateCartCounter() {
        const cart = JSON.parse(localStorage.getItem('restaurant-cart') || '[]');
        const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);

        const cartCounter = document.getElementById('cartCounter');
        if (cartCounter) {
            cartCounter.textContent = totalItems;
            cartCounter.style.display = totalItems > 0 ? 'block' : 'none';
        }
    }
    
    // Initialize about page
    initAboutPage() {
        // Add any about page specific functionality
        this.initCounterAnimations();
    }
    
    // Initialize contact page
    initContactPage() {
        this.initMap();
        this.initContactForm();
    }
    
    // Initialize counter animations
    initCounterAnimations() {
        const counters = document.querySelectorAll('.counter');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const duration = 2000;
            const increment = target / (duration / 16);
            let current = 0;
            
            const updateCounter = () => {
                current += increment;
                if (current < target) {
                    counter.textContent = Math.floor(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };
            
            // Start animation when element is visible
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        updateCounter();
                        observer.unobserve(entry.target);
                    }
                });
            });
            
            observer.observe(counter);
        });
    }
    
    // Handle form submission
    handleFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());
        
        // Show loading state
        const submitButton = form.querySelector('button[type="submit"]');
        const originalText = submitButton.textContent;
        submitButton.textContent = translate('loading');
        submitButton.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
            this.showNotification(translate('message_sent'), 'success');
            form.reset();
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }, 2000);
    }
    
    // Show notification
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Get social platform from link
    getSocialPlatform(link) {
        const classList = link.classList;
        if (classList.contains('facebook')) return 'facebook';
        if (classList.contains('instagram')) return 'instagram';
        if (classList.contains('twitter')) return 'twitter';
        if (classList.contains('whatsapp')) return 'whatsapp';
        return 'unknown';
    }
    
    // Open social media
    openSocialMedia(platform) {
        const urls = {
            facebook: 'https://facebook.com/restaurant-gourmet',
            instagram: 'https://instagram.com/restaurant_gourmet',
            twitter: 'https://twitter.com/restaurant_gourmet',
            whatsapp: 'https://wa.me/966111234567'
        };
        
        if (urls[platform]) {
            window.open(urls[platform], '_blank');
        }
    }
    
    // Show dish details modal
    showDishDetails(dishId) {
        if (!window.menuData) return;
        
        const dish = menuData.getDishById(parseInt(dishId));
        if (!dish) return;
        
        // Create modal (simplified version)
        const modal = document.createElement('div');
        modal.className = 'dish-modal';
        modal.innerHTML = `
            <div class="modal-content">
                <button class="modal-close">&times;</button>
                <img src="${dish.image}" alt="${getLocalizedContent(dish.name)}">
                <h2>${getLocalizedContent(dish.name)}</h2>
                <p>${getLocalizedContent(dish.description)}</p>
                <div class="price">${formatCurrency(dish.price)}</div>
                <button class="btn btn-primary">${translate('add_to_cart')}</button>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Close modal events
        const closeBtn = modal.querySelector('.modal-close');
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(modal);
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
            }
        });
    }
    
    // Handle language change
    handleLanguageChange(language) {
        // Reload dynamic content with new language
        if (this.currentPage === 'index') {
            this.loadPopularDishes();
        }
    }
    
    // Handle window resize
    handleResize() {
        // Handle responsive adjustments
        const isMobile = window.innerWidth <= 768;
        document.body.classList.toggle('mobile', isMobile);
    }
    
    // Debounce utility function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.restaurantApp = new RestaurantApp();
});

// Global utility functions
function showLoading(element) {
    element.classList.add('loading');
}

function hideLoading(element) {
    element.classList.remove('loading');
}

function animateElement(element, animation) {
    element.classList.add(animation);
    element.addEventListener('animationend', () => {
        element.classList.remove(animation);
    }, { once: true });
}
