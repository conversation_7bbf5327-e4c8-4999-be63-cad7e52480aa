# مطعم الذواقة - موقع إلكتروني احترافي
# Gourmet Restaurant - Professional Website

موقع إلكتروني احترافي لمطعم الذواقة مع دعم اللغتين العربية والإنجليزية، تصميم متجاوب، وتأثيرات حركية جذابة.

A professional website for Gourmet Restaurant with bilingual support (Arabic/English), responsive design, and attractive animations.

## ✨ الميزات الرئيسية / Key Features

### 🌐 دعم اللغات / Language Support
- **العربية والإنجليزية** مع تبديل سلس بين اللغات
- **RTL/LTR Support** - دعم كامل لاتجاه النص
- **ترجمة ديناميكية** لجميع عناصر الموقع

### 📱 تصميم متجاوب / Responsive Design
- متوافق مع جميع الأجهزة (هاتف، تابلت، كمبيوتر)
- تصميم حديث وأنيق
- تجربة مستخدم محسنة

### 🎨 تأثيرات بصرية / Visual Effects
- حركات انتقال سلسة
- تأثيرات hover تفاعلية
- رسوم متحركة عند التمرير
- تحميل متدرج للمحتوى

### 🍽️ إدارة المنيو / Menu Management
- عرض الأطباق بفئات منظمة
- بحث في المنيو
- تصفية حسب الفئة
- تفاصيل مفصلة لكل طبق

## 📁 هيكل المشروع / Project Structure

```
restaurant-website/
├── index.html              # الصفحة الرئيسية / Home Page
├── menu.html               # صفحة المنيو / Menu Page
├── about.html              # صفحة حولنا / About Page
├── contact.html            # صفحة التواصل / Contact Page
├── css/
│   ├── style.css          # التصميم الرئيسي / Main Styles
│   └── animations.css     # الحركات والتأثيرات / Animations
├── js/
│   ├── main.js           # الوظائف الرئيسية / Main Functions
│   └── language.js       # إدارة اللغات / Language Management
├── data/
│   ├── translations.js   # ملف الترجمات / Translations
│   └── menu-data.js      # بيانات المنيو / Menu Data
├── images/               # مجلد الصور / Images Folder
│   └── README.md        # دليل الصور المطلوبة / Images Guide
└── README.md            # هذا الملف / This File
```

## 🚀 كيفية التشغيل / How to Run

### 1. تحميل المشروع / Download Project
```bash
# استنسخ المشروع أو حمل الملفات
# Clone the project or download files
```

### 2. إضافة الصور / Add Images
- راجع ملف `images/README.md` لقائمة الصور المطلوبة
- أضف الصور في المجلدات المناسبة
- Check `images/README.md` for required images list
- Add images to appropriate folders

### 3. فتح الموقع / Open Website
- افتح `index.html` في المتصفح
- أو استخدم خادم محلي مثل Live Server
- Open `index.html` in browser
- Or use a local server like Live Server

## 📄 الصفحات المتاحة / Available Pages

### 🏠 الصفحة الرئيسية / Home Page (`index.html`)
- قسم ترحيبي جذاب
- عرض الميزات الرئيسية
- أطباق مميزة
- آراء العملاء

### 🍽️ صفحة المنيو / Menu Page (`menu.html`)
- عرض جميع الأطباق
- تصفية حسب الفئة
- بحث في المنيو
- عروض خاصة

### ℹ️ صفحة حولنا / About Page (`about.html`)
- قصة المطعم
- الرؤية والرسالة
- فريق العمل
- الإنجازات والإحصائيات

### 📞 صفحة التواصل / Contact Page (`contact.html`)
- معلومات التواصل
- نموذج اتصال
- خريطة الموقع
- ساعات العمل

## 🎨 التخصيص / Customization

### تغيير الألوان / Change Colors
```css
:root {
    --primary-color: #d4af37;    /* اللون الأساسي */
    --secondary-color: #8b4513;  /* اللون الثانوي */
    --accent-color: #ff6b35;     /* لون التمييز */
}
```

### إضافة أطباق جديدة / Add New Dishes
```javascript
// في ملف data/menu-data.js
// In data/menu-data.js file
const newDish = {
    id: 16,
    category: 'main_courses',
    name: {
        ar: 'اسم الطبق بالعربية',
        en: 'Dish Name in English'
    },
    // ... باقي البيانات
};
```

### تعديل الترجمات / Edit Translations
```javascript
// في ملف data/translations.js
// In data/translations.js file
const translations = {
    ar: {
        new_key: "النص بالعربية"
    },
    en: {
        new_key: "Text in English"
    }
};
```

## 🔧 التقنيات المستخدمة / Technologies Used

- **HTML5** - هيكل الصفحات
- **CSS3** - التصميم والتنسيق
- **JavaScript (ES6+)** - التفاعل والوظائف
- **Font Awesome** - الأيقونات
- **Google Fonts** - الخطوط

## 📱 التوافق / Compatibility

### المتصفحات المدعومة / Supported Browsers
- ✅ Chrome 60+
- ✅ Firefox 55+
- ✅ Safari 12+
- ✅ Edge 79+

### الأجهزة / Devices
- ✅ أجهزة الكمبيوتر / Desktop
- ✅ الأجهزة اللوحية / Tablets
- ✅ الهواتف الذكية / Mobile Phones

## 🚀 تحسينات الأداء / Performance Optimizations

- **تحميل كسول للصور** - Lazy loading for images
- **ضغط CSS و JavaScript** - Minified CSS & JavaScript
- **تحسين الخطوط** - Optimized font loading
- **تخزين مؤقت ذكي** - Smart caching

## 🔒 الأمان / Security

- **تنظيف المدخلات** - Input sanitization
- **حماية من XSS** - XSS protection
- **رؤوس أمان** - Security headers
- **التحقق من النماذج** - Form validation

## 📈 تحسين محركات البحث / SEO

- **علامات Meta محسنة** - Optimized meta tags
- **بنية HTML دلالية** - Semantic HTML structure
- **خريطة الموقع** - Sitemap ready
- **بيانات منظمة** - Structured data ready

## 🤝 المساهمة / Contributing

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

We welcome contributions! Please:
1. Fork the project
2. Create a feature branch
3. Make your changes
4. Submit a Pull Request

## 📄 الترخيص / License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 الدعم / Support

للحصول على الدعم أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

For support or to report issues:
- Create an Issue on GitHub
- Contact via email

## 🙏 شكر وتقدير / Acknowledgments

- Font Awesome للأيقونات الرائعة
- Google Fonts للخطوط الجميلة
- المجتمع المفتوح المصدر للإلهام

---

**تم تطوير هذا المشروع بحب ❤️ لمجتمع المطاعم العربية**

**This project was developed with love ❤️ for the Arabic restaurant community**
