// Menu data for the restaurant
const menuData = {
    categories: [
        {
            id: 'appetizers',
            name: {
                ar: 'المقبلات',
                en: 'Appetizers'
            },
            icon: 'fas fa-leaf'
        },
        {
            id: 'main_courses',
            name: {
                ar: 'الأطباق الرئيسية',
                en: 'Main Courses'
            },
            icon: 'fas fa-utensils'
        },
        {
            id: 'desserts',
            name: {
                ar: 'الحلويات',
                en: 'Desserts'
            },
            icon: 'fas fa-ice-cream'
        },
        {
            id: 'beverages',
            name: {
                ar: 'المشروبات',
                en: 'Beverages'
            },
            icon: 'fas fa-coffee'
        }
    ],
    
    dishes: [
        // Appetizers
        {
            id: 1,
            category: 'appetizers',
            name: {
                ar: 'حمص بالطحينة',
                en: 'Hummus with Tahini'
            },
            description: {
                ar: 'حمص كريمي مع الطحينة وزيت الزيتون والصنوبر',
                en: 'Creamy hummus with tahini, olive oil, and pine nuts'
            },
            price: 25,
            image: 'images/dishes/hummus.jpg',
            popular: true,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['حمص', 'طحينة', 'زيت زيتون', 'صنوبر', 'ثوم', 'ليمون'],
                en: ['Chickpeas', 'Tahini', 'Olive Oil', 'Pine Nuts', 'Garlic', 'Lemon']
            }
        },
        {
            id: 2,
            category: 'appetizers',
            name: {
                ar: 'تبولة',
                en: 'Tabbouleh'
            },
            description: {
                ar: 'سلطة البقدونس الطازجة مع الطماطم والبرغل وعصير الليمون',
                en: 'Fresh parsley salad with tomatoes, bulgur, and lemon juice'
            },
            price: 30,
            image: 'images/dishes/tabbouleh.jpg',
            popular: true,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['بقدونس', 'طماطم', 'برغل', 'بصل أخضر', 'نعناع', 'ليمون'],
                en: ['Parsley', 'Tomatoes', 'Bulgur', 'Green Onions', 'Mint', 'Lemon']
            }
        },
        {
            id: 3,
            category: 'appetizers',
            name: {
                ar: 'فتوش',
                en: 'Fattoush'
            },
            description: {
                ar: 'سلطة مشكلة مع الخبز المحمص والسماق',
                en: 'Mixed salad with toasted bread and sumac'
            },
            price: 28,
            image: 'images/dishes/fattoush.jpg',
            popular: false,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['خس', 'طماطم', 'خيار', 'فجل', 'خبز محمص', 'سماق'],
                en: ['Lettuce', 'Tomatoes', 'Cucumber', 'Radish', 'Toasted Bread', 'Sumac']
            }
        },
        {
            id: 4,
            category: 'appetizers',
            name: {
                ar: 'بابا غنوج',
                en: 'Baba Ganoush'
            },
            description: {
                ar: 'متبل الباذنجان المشوي مع الطحينة والثوم',
                en: 'Grilled eggplant dip with tahini and garlic'
            },
            price: 27,
            image: 'images/dishes/baba-ganoush.jpg',
            popular: false,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['باذنجان مشوي', 'طحينة', 'ثوم', 'ليمون', 'زيت زيتون'],
                en: ['Grilled Eggplant', 'Tahini', 'Garlic', 'Lemon', 'Olive Oil']
            }
        },
        
        // Main Courses
        {
            id: 5,
            category: 'main_courses',
            name: {
                ar: 'دجاج مشوي',
                en: 'Grilled Chicken'
            },
            description: {
                ar: 'دجاج مشوي بالأعشاب والتوابل مع الأرز والخضار',
                en: 'Herb and spice grilled chicken with rice and vegetables'
            },
            price: 65,
            image: 'images/dishes/grilled-chicken.jpg',
            popular: true,
            vegetarian: false,
            spicy: false,
            ingredients: {
                ar: ['دجاج', 'أرز بسمتي', 'خضار مشكلة', 'أعشاب', 'توابل'],
                en: ['Chicken', 'Basmati Rice', 'Mixed Vegetables', 'Herbs', 'Spices']
            }
        },
        {
            id: 6,
            category: 'main_courses',
            name: {
                ar: 'كبسة لحم',
                en: 'Lamb Kabsa'
            },
            description: {
                ar: 'أرز بسمتي مع لحم الخروف والتوابل العربية الأصيلة',
                en: 'Basmati rice with lamb and authentic Arabic spices'
            },
            price: 85,
            image: 'images/dishes/lamb-kabsa.jpg',
            popular: true,
            vegetarian: false,
            spicy: true,
            ingredients: {
                ar: ['لحم خروف', 'أرز بسمتي', 'بصل', 'طماطم', 'توابل كبسة'],
                en: ['Lamb', 'Basmati Rice', 'Onions', 'Tomatoes', 'Kabsa Spices']
            }
        },
        {
            id: 7,
            category: 'main_courses',
            name: {
                ar: 'صيادية سمك',
                en: 'Fish Sayadieh'
            },
            description: {
                ar: 'سمك طازج مع الأرز والبصل المكرمل',
                en: 'Fresh fish with rice and caramelized onions'
            },
            price: 75,
            image: 'images/dishes/fish-sayadieh.jpg',
            popular: false,
            vegetarian: false,
            spicy: false,
            ingredients: {
                ar: ['سمك طازج', 'أرز', 'بصل مكرمل', 'توابل', 'صنوبر'],
                en: ['Fresh Fish', 'Rice', 'Caramelized Onions', 'Spices', 'Pine Nuts']
            }
        },
        {
            id: 8,
            category: 'main_courses',
            name: {
                ar: 'مندي لحم',
                en: 'Lamb Mandi'
            },
            description: {
                ar: 'لحم خروف مطبوخ على الفحم مع الأرز المدخن',
                en: 'Charcoal-cooked lamb with smoked rice'
            },
            price: 90,
            image: 'images/dishes/lamb-mandi.jpg',
            popular: true,
            vegetarian: false,
            spicy: true,
            ingredients: {
                ar: ['لحم خروف', 'أرز مدخن', 'توابل مندي', 'بصل', 'زبيب'],
                en: ['Lamb', 'Smoked Rice', 'Mandi Spices', 'Onions', 'Raisins']
            }
        },
        
        // Desserts
        {
            id: 9,
            category: 'desserts',
            name: {
                ar: 'بقلاوة',
                en: 'Baklava'
            },
            description: {
                ar: 'حلوى شرقية بالفستق والعسل',
                en: 'Middle Eastern pastry with pistachios and honey'
            },
            price: 35,
            image: 'images/dishes/baklava.jpg',
            popular: true,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['عجينة فيلو', 'فستق', 'عسل', 'سكر', 'زبدة'],
                en: ['Phyllo Dough', 'Pistachios', 'Honey', 'Sugar', 'Butter']
            }
        },
        {
            id: 10,
            category: 'desserts',
            name: {
                ar: 'مهلبية',
                en: 'Muhallabia'
            },
            description: {
                ar: 'حلوى الحليب التقليدية مع ماء الورد والفستق',
                en: 'Traditional milk pudding with rose water and pistachios'
            },
            price: 25,
            image: 'images/dishes/muhallabia.jpg',
            popular: false,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['حليب', 'سكر', 'نشا', 'ماء ورد', 'فستق'],
                en: ['Milk', 'Sugar', 'Starch', 'Rose Water', 'Pistachios']
            }
        },
        {
            id: 11,
            category: 'desserts',
            name: {
                ar: 'كنافة',
                en: 'Knafeh'
            },
            description: {
                ar: 'كنافة نابلسية بالجبن والقطر',
                en: 'Nabulsi knafeh with cheese and syrup'
            },
            price: 40,
            image: 'images/dishes/knafeh.jpg',
            popular: true,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['كنافة', 'جبن نابلسي', 'قطر', 'فستق', 'سمن'],
                en: ['Knafeh Dough', 'Nabulsi Cheese', 'Syrup', 'Pistachios', 'Ghee']
            }
        },
        
        // Beverages
        {
            id: 12,
            category: 'beverages',
            name: {
                ar: 'قهوة عربية',
                en: 'Arabic Coffee'
            },
            description: {
                ar: 'قهوة عربية أصيلة مع الهيل',
                en: 'Authentic Arabic coffee with cardamom'
            },
            price: 15,
            image: 'images/dishes/arabic-coffee.jpg',
            popular: true,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['قهوة عربية', 'هيل', 'زعفران'],
                en: ['Arabic Coffee', 'Cardamom', 'Saffron']
            }
        },
        {
            id: 13,
            category: 'beverages',
            name: {
                ar: 'شاي بالنعناع',
                en: 'Mint Tea'
            },
            description: {
                ar: 'شاي أخضر طازج بالنعناع',
                en: 'Fresh green tea with mint'
            },
            price: 12,
            image: 'images/dishes/mint-tea.jpg',
            popular: false,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['شاي أخضر', 'نعناع طازج', 'سكر'],
                en: ['Green Tea', 'Fresh Mint', 'Sugar']
            }
        },
        {
            id: 14,
            category: 'beverages',
            name: {
                ar: 'عصير برتقال طازج',
                en: 'Fresh Orange Juice'
            },
            description: {
                ar: 'عصير برتقال طبيعي طازج',
                en: 'Fresh natural orange juice'
            },
            price: 18,
            image: 'images/dishes/orange-juice.jpg',
            popular: true,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['برتقال طازج'],
                en: ['Fresh Oranges']
            }
        },
        {
            id: 15,
            category: 'beverages',
            name: {
                ar: 'عصير مانجو',
                en: 'Mango Juice'
            },
            description: {
                ar: 'عصير مانجو طبيعي منعش',
                en: 'Refreshing natural mango juice'
            },
            price: 20,
            image: 'images/dishes/mango-juice.jpg',
            popular: false,
            vegetarian: true,
            spicy: false,
            ingredients: {
                ar: ['مانجو طازج', 'ثلج'],
                en: ['Fresh Mango', 'Ice']
            }
        }
    ],
    
    // Popular dishes for homepage
    getPopularDishes: function() {
        return this.dishes.filter(dish => dish.popular);
    },
    
    // Get dishes by category
    getDishesByCategory: function(categoryId) {
        return this.dishes.filter(dish => dish.category === categoryId);
    },
    
    // Get dish by ID
    getDishById: function(dishId) {
        return this.dishes.find(dish => dish.id === dishId);
    },
    
    // Search dishes
    searchDishes: function(query, language = 'ar') {
        const searchTerm = query.toLowerCase();
        return this.dishes.filter(dish => 
            dish.name[language].toLowerCase().includes(searchTerm) ||
            dish.description[language].toLowerCase().includes(searchTerm)
        );
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = menuData;
}
